import uuid
from django.db import models
from django.core.exceptions import ValidationError
from clients.models import Client
from vendeurs.models import Vendeur
from stock_Pme.models import Stock
from magasins.models import Magasin
from produit.models import Produit
from entreprise.models import Entreprise
import logging
from decimal import Decimal

logger = logging.getLogger(__name__)

class Vente(models.Model):
    MODE_PAIEMENT_CHOICES = [
        ('ESPECES', 'Espèces'),
        ('CARTE', 'Carte bancaire'),
        ('CHEQUE', 'Chèque'),
        ('VIREMENT', 'Virement'),
    ]
    
    id_vente = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    entreprise = models.ForeignKey(Entreprise, on_delete=models.CASCADE, related_name='ventes', null=True, blank=True)
    client = models.ForeignKey(Client, on_delete=models.SET_NULL, null=True, blank=True)
    vendeur = models.ForeignKey(Vendeur, on_delete=models.SET_NULL, null=True)
    magasin = models.ForeignKey(Magasin, on_delete=models.CASCADE, related_name='ventes')
    montant_total = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    montant_ht = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    montant_tva = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    date_vente = models.DateTimeField(auto_now_add=True)
    mode_paiement = models.CharField(max_length=20, choices=MODE_PAIEMENT_CHOICES, default='ESPECES')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Vente"
        verbose_name_plural = "Ventes"
        indexes = [
            models.Index(fields=['date_vente']),
            models.Index(fields=['client']),
            models.Index(fields=['vendeur']),
            models.Index(fields=['magasin']),
        ]

    def __str__(self):
        return f"Vente #{self.id_vente} - {self.montant_total}"

    def clean(self):
        if self.montant_total < 0:
            raise ValidationError("Le montant total ne peut pas être négatif.")
        if self.vendeur and self.vendeur.magasin != self.magasin:
            raise ValidationError("Le vendeur doit appartenir au magasin de la vente.")

    @classmethod
    def creer_vente(cls, client_id, vendeur_id, magasin_id, data=None):
        """Crée une nouvelle vente avec les informations de base"""
        client = Client.objects.get(id=client_id) if client_id else None
        vendeur = Vendeur.objects.get(id=vendeur_id) if vendeur_id else None
        magasin = Magasin.objects.get(id=magasin_id)
        
        if vendeur and vendeur.magasin != magasin:
            raise ValidationError("Le vendeur doit appartenir au magasin spécifié.")
        
        return cls.objects.create(
            client=client,
            vendeur=vendeur,
            magasin=magasin,
            montant_total=0,
            montant_ht=0,
            montant_tva=0
        )

    def calculer_montant_total(self):
        """Calcule le montant total de la vente en fonction des détails"""
        from django.db.models import Sum
        from decimal import Decimal

        # Calculer les totaux à partir des détails
        resultats = self.details.aggregate(
            total_ht=Sum('montant_ht'),
            total_tva=Sum('montant_tva'),
            total=Sum('montant_total')
        )

        # Mettre à jour les montants
        self.montant_ht = resultats['total_ht'] or Decimal('0.00')
        self.montant_tva = resultats['total_tva'] or Decimal('0.00')
        self.montant_total = resultats['total'] or Decimal('0.00')
        
        # Sauvegarder les modifications
        self.save(update_fields=['montant_ht', 'montant_tva', 'montant_total'])
        
        return self.montant_total

    def save(self, *args, **kwargs):
        # Si l'entreprise n'est pas spécifiée, utiliser celle du magasin
        if not self.entreprise and self.magasin:
            self.entreprise = self.magasin.entreprise
        super().save(*args, **kwargs)

class DetailVente(models.Model):
    id_detail_vente = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    vente = models.ForeignKey(Vente, on_delete=models.CASCADE, related_name='details')
    produit = models.ForeignKey(Produit, on_delete=models.CASCADE)
    quantite_vendue = models.PositiveIntegerField()
    prix_unitaire = models.DecimalField(max_digits=10, decimal_places=2)
    prix_achat = models.DecimalField(max_digits=10, decimal_places=2)
    taux_tva = models.DecimalField(max_digits=5, decimal_places=2, default=20.00)
    montant_ht = models.DecimalField(max_digits=10, decimal_places=2)
    montant_tva = models.DecimalField(max_digits=10, decimal_places=2)
    montant_total = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Détail Vente"
        verbose_name_plural = "Détails Ventes"
        indexes = [
            models.Index(fields=['vente']),
            models.Index(fields=['produit']),
        ]

    def __str__(self):
        return f"{self.quantite_vendue} x {self.produit.nom} - {self.montant_total}"

    def clean(self):
        if self.quantite_vendue <= 0:
            raise ValidationError("La quantité vendue doit être supérieure à 0.")
        if self.prix_unitaire <= 0:
            raise ValidationError("Le prix unitaire doit être supérieur à 0.")
        if self.montant_total != self.quantite_vendue * self.prix_unitaire:
            raise ValidationError("Le montant total doit être égal à quantité_vendue * prix_unitaire.")

    def save(self, *args, **kwargs):
        # Calcul des montants
        self.montant_ht = self.quantite_vendue * self.prix_unitaire
        self.montant_tva = self.montant_ht * (self.taux_tva / Decimal('100'))
        self.montant_total = self.montant_ht + self.montant_tva
        
        # Sauvegarder le détail
        super().save(*args, **kwargs)
        
        # Mettre à jour les montants de la vente
        self.vente.calculer_montant_total()

    @classmethod
    def ajouterProduit(cls, id_vente, id_produit, quantite):
        """Ajoute un produit à une vente et met à jour le stock"""
        from decimal import Decimal
        vente = Vente.objects.get(id_vente=id_vente)
        produit = Produit.objects.get(id_produit=id_produit)
        magasin_principal = Magasin.objects.filter(actif=True).first()
        if not magasin_principal:
            logger.error("Aucun magasin actif trouvé")
            raise ValidationError("Aucun magasin actif trouvé")

        # Vérifier le stock
        try:
            stock = Stock.objects.get(
                produit=produit,
                magasin=magasin_principal,
                location__type_stock=produit.type_stock
            )
            if stock.quantite_disponible < quantite:
                logger.error(f"Stock insuffisant pour le produit {produit.nom}")
                raise ValidationError(f"Stock insuffisant pour le produit {produit.nom}")
        except Stock.DoesNotExist:
            logger.error(f"Aucun stock trouvé pour le produit {produit.nom} dans le magasin {magasin_principal.nom}")
            raise ValidationError(f"Aucun stock trouvé pour le produit {produit.nom}")

        # Créer le détail de vente
        prix_unitaire = produit.prix_vente
        prix_achat = produit.prix_achat
        taux_tva = Decimal(str(produit.TVA)) if hasattr(produit, 'TVA') else Decimal('0.20')
        
        # Calculer les montants
        montant_ht = quantite * prix_unitaire
        montant_tva = montant_ht * taux_tva
        montant_total = montant_ht + montant_tva
        
        detail = cls.objects.create(
            vente=vente,
            produit=produit,
            quantite_vendue=quantite,
            prix_unitaire=prix_unitaire,
            prix_achat=prix_achat,
            taux_tva=taux_tva,
            montant_ht=montant_ht,
            montant_tva=montant_tva,
            montant_total=montant_total
        )

        # Mettre à jour le stock
        stock.retirerStock(quantite)

        # Mettre à jour les montants totaux de la vente
        vente.calculer_montant_total()

        return detail

    def retirerProduit(self):
        """Retire un produit de la vente et restaure le stock"""
        magasin_principal = Magasin.objects.filter(actif=True).first()
        if not magasin_principal:
            logger.error("Aucun magasin actif trouvé")
            raise ValidationError("Aucun magasin actif trouvé")

        stock = Stock.objects.filter(entrepot__type_stock=self.produit.type_stock, produit=self.produit, magasin=magasin_principal).first()
        if stock:
            stock.ajouter_produit(self.quantite_vendue)
        else:
            logger.warning(f"Aucun stock trouvé pour restaurer le produit {self.produit.nom}")

        vente = self.vente
        self.delete()
        return vente