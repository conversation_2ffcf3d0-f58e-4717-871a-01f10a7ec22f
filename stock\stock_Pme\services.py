from entrepot.models import Entrepot, Location
from stock_Pme.models import Stock, MouvementStock
from django.db.models import Sum

class StockService:
    @staticmethod
    def verifier_capacite_entrepot(entrepot):
        """Vérifie la capacité restante de l'entrepôt"""
        total_stock = Stock.objects.filter(
            location__entrepot=entrepot
        ).aggregate(total=Sum('quantite_disponible'))['total'] or 0
        return {
            'capacite_max': entrepot.capacite_stockage,
            'stock_actuel': total_stock,
            'espace_disponible': entrepot.capacite_stockage - total_stock,
            'depassement': total_stock > entrepot.capacite_stockage
        }

    @staticmethod
    def generer_rapport_stock(entrepot):
        """Génère un rapport détaillé du stock"""
        stocks = Stock.objects.filter(
            location__entrepot=entrepot
        ).select_related('product', 'location')
        
        return {
            'entrepot': {
                'id': entrepot.id_entrepot,
                'nom': entrepot.nom,
                'type': entrepot.get_type_stock_display()
            },
            'stocks': [{
                'produit_id': stock.product.id_produit,
                'produit_nom': stock.product.nom,
                'reference': stock.product.reference,
                'quantite': stock.quantite_disponible,
                'seuil_alerte': stock.seuil_alerte,
                'en_alerte': stock.quantite_disponible < stock.seuil_alerte,
                'emplacement': stock.location.nom
            } for stock in stocks]
        }

    @staticmethod
    def executer_mouvement(produit_id, quantite, type_mouvement, utilisateur, notes=None, entrepot_id=None, magasin_id=None):
        """Gère un mouvement de stock avec location automatique basée sur le type de stock du produit"""
        from produit.models import Produit
        from magasins.models import Magasin
        
        # Récupérer le produit
        try:
            produit = Produit.objects.get(id_produit=produit_id)
        except Produit.DoesNotExist:
            raise ValueError("Produit non trouvé")

        # Récupérer le magasin
        try:
            magasin = Magasin.objects.get(id=magasin_id) if magasin_id else Magasin.objects.filter(actif=True).first()
            if not magasin:
                raise ValueError("Aucun magasin actif trouvé")
        except Magasin.DoesNotExist:
            raise ValueError("Magasin non trouvé")

        # Trouver l'entrepôt approprié
        if entrepot_id:
            try:
                entrepot = Entrepot.objects.get(id_entrepot=entrepot_id)
            except Entrepot.DoesNotExist:
                raise ValueError("Entrepôt non trouvé")
        else:
            # Trouver le premier entrepôt actif
            entrepot = Entrepot.objects.filter(statut=True).first()
            if not entrepot:
                raise ValueError("Aucun entrepôt actif trouvé")

        # Trouver la location appropriée dans l'entrepôt
        location = Location.objects.filter(
            entrepot=entrepot,
            type_stock=produit.type_stock
        ).first()
        
        if not location:
            raise ValueError(f"Aucune location trouvée pour le type de stock {produit.type_stock} dans l'entrepôt {entrepot.nom}")

        # Validation
        if quantite <= 0:
            raise ValueError("La quantité doit être positive")
        
        # Création du mouvement
        mouvement = MouvementStock.objects.create(
            produit=produit,
            location=location,
            quantite=quantite,
            type_mouvement=type_mouvement,
            utilisateur=utilisateur,
            notes=notes
        )
        
        # Mise à jour du stock
        stock, created = Stock.objects.get_or_create(
            produit=produit,
            location=location,
            magasin=magasin,
            defaults={
                'quantite_disponible': 0,
                'seuil_alerte': 10,  # Valeur par défaut
                'entrepot': entrepot
            }
        )
        
        if type_mouvement == 'ENTREE':
            stock.ajouterStock(quantite)
        elif type_mouvement == 'SORTIE':
            if not stock.retirerStock(quantite):
                mouvement.delete()
                raise ValueError("Stock insuffisant")
        
        return {
            'mouvement': mouvement,
            'stock': stock,
            'nouvelle_quantite': stock.quantite_disponible,
            'location': location.nom,
            'entrepot': entrepot.nom,
            'magasin': magasin.nom
        }