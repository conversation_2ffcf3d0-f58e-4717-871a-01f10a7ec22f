from rest_framework import serializers
from django.db import models
from .models import Produit
from categorie.serializers import CategorieSerializer
from categorie.models import Categorie
from magasins.models import Magasin

class ProduitSerializer(serializers.ModelSerializer):
    categorie = CategorieSerializer(read_only=True)
    categorie_nom = serializers.CharField(write_only=True, required=True)
    stock_total = serializers.SerializerMethodField()
    nom_magasin = serializers.CharField(write_only=True, required=True)
    entreprise_magasin = serializers.SerializerMethodField()
    entreprise = serializers.SerializerMethodField()

    class Meta:
        model = Produit
        fields = [
            'id_produit', 'nom', 'reference', 'categorie', 'categorie_nom', 'type_stock',
            'prix', 'date_peremption', 'code_barre', 'marque', 'description', 'unite_mesure',
            'prix_achat', 'prix_vente', 'TVA', 'created_at', 'updated_at', 'stock_total', 
            'nom_magasin', 'entreprise_magasin', 'entreprise'
        ]

    def get_stock_total(self, obj):
       from stock_Pme.models import Stock
       stock_total = Stock.objects.filter(produit=obj).aggregate(total=models.Sum('quantite_disponible'))['total'] or 0
       return stock_total

    def get_entreprise_magasin(self, obj):
        """Retourne l'entreprise du magasin qui contient le produit"""
        if obj.magasin and obj.magasin.entreprise:
            return {
                'id': obj.magasin.entreprise.id,
                'nom': obj.magasin.entreprise.nom,
                'description': obj.magasin.entreprise.description
            }
        return None

    def get_entreprise(self, obj):
        """Retourne l'entreprise du produit"""
        if obj.entreprise:
            return {
                'id': obj.entreprise.id,
                'nom': obj.entreprise.nom,
                'description': obj.entreprise.description
            }
        return None

    def validate_reference(self, value):
        if Produit.objects.filter(reference=value).exclude(id_produit=self.instance.id_produit if self.instance else None).exists():
            raise serializers.ValidationError("Cette référence est déjà utilisée.")
        return value

    def validate_code_barre(self, value):
        if value and Produit.objects.filter(code_barre=value).exclude(id_produit=self.instance.id_produit if self.instance else None).exists():
            raise serializers.ValidationError("Ce code-barre est déjà utilisé.")
        return value

    def validate_prix(self, value):
        if value <= 0:
            raise serializers.ValidationError("Le prix doit être supérieur à 0.")
        return value

    def validate_prix_achat(self, value):
        if value <= 0:
            raise serializers.ValidationError("Le prix d'achat doit être supérieur à 0.")
        return value

    def validate_prix_vente(self, value):
        if value <= 0:
            raise serializers.ValidationError("Le prix de vente doit être supérieur à 0.")
        return value

    def validate_TVA(self, value):
        if value < 0:
            raise serializers.ValidationError("La TVA ne peut pas être négative.")
        return value

    def validate(self, data):
        if not data.get('categorie_nom'):
            raise serializers.ValidationError("Vous devez fournir un nom de catégorie.")
        return data

    def create(self, validated_data):
        # Retirer categorie_nom et nom_magasin des données validées
        categorie_nom = validated_data.pop('categorie_nom')
        nom_magasin = validated_data.pop('nom_magasin')
        
        # Trouver le magasin par son nom
        try:
            magasin = Magasin.objects.get(nom=nom_magasin)
            validated_data['magasin'] = magasin
            # Récupérer l'entreprise associée au magasin
            validated_data['entreprise'] = magasin.entreprise
        except Magasin.DoesNotExist:
            raise serializers.ValidationError(f"Aucun magasin trouvé avec le nom : {nom_magasin}")
        
        # Créer ou récupérer la catégorie
        try:
            # Essayer de trouver une catégorie existante
            categorie = Categorie.objects.get(nom=categorie_nom)
        except Categorie.DoesNotExist:
            # Créer une nouvelle catégorie si elle n'existe pas
            categorie = Categorie.objects.create(nom=categorie_nom)
        validated_data['categorie'] = categorie
        
        # Créer le produit
        return Produit.objects.create(**validated_data)