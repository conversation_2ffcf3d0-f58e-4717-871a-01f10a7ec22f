from rest_framework import serializers
from .models import Magasin, LocalisationMagasin
from entreprise.serializers import EntrepriseSerializer
from responsable_magasin.models import Responsable_magasin
from entreprise.models import Entreprise
from entrepot.models import Entrepot
import uuid

class LocalisationMagasinSerializer(serializers.ModelSerializer):
    class Meta:
        model = LocalisationMagasin
        fields = ['id', 'adresse', 'ville', 'code_postal', 'pays', 'telephone', 'email', 'actif']

    def validate_telephone(self, value):
        qs = LocalisationMagasin.objects.filter(telephone=value)
        if self.instance:
            qs = qs.exclude(pk=self.instance.pk)
        if qs.exists():
            raise serializers.ValidationError("Ce numéro de téléphone est déjà utilisé.")
        return value

    def validate_email(self, value):
        if value:
            qs = LocalisationMagasin.objects.filter(email=value)
            if self.instance:
                qs = qs.exclude(pk=self.instance.pk)
            if qs.exists():
                raise serializers.ValidationError("Cet email est déjà utilisé.")
        return value

class MagasinSerializer(serializers.ModelSerializer):
    localisation = LocalisationMagasinSerializer(required=True)
    entreprise_details = EntrepriseSerializer(source='entreprise', read_only=True)
    rapport_ventes = serializers.SerializerMethodField()
    responsable_magasin = serializers.UUIDField(required=False, allow_null=True)
    entreprise = serializers.UUIDField()
    entrepot = serializers.UUIDField(required=False, allow_null=True)

    class Meta:
        model = Magasin
        fields = [
            'id', 'nom', 'localisation', 'entreprise', 'entreprise_details',
            'responsable_magasin', 'actif', 'created_at', 'updated_at', 'rapport_ventes',
            'entrepot'
        ]

    def get_rapport_ventes(self, obj):
        return obj.generer_rapport_ventes()

    def create(self, validated_data):
        localisation_data = validated_data.pop('localisation', None)
        if not localisation_data:
            raise serializers.ValidationError({"localisation": "Ce champ est requis."})

        # Convertir les UUIDs en objets
        if 'responsable_magasin' in validated_data and validated_data['responsable_magasin']:
            validated_data['responsable_magasin'] = Responsable_magasin.objects.get(id=validated_data['responsable_magasin'])
        else:
            validated_data['responsable_magasin'] = None

        validated_data['entreprise'] = Entreprise.objects.get(id=validated_data['entreprise'])
        
        if 'entrepot' in validated_data and validated_data['entrepot']:
            validated_data['entrepot'] = Entrepot.objects.get(id=validated_data['entrepot'])
        else:
            validated_data['entrepot'] = None

        # Créer d'abord la localisation
        localisation = LocalisationMagasin.objects.create(**localisation_data)
        validated_data['localisation'] = localisation

        # Créer le magasin avec la localisation
        magasin = Magasin.objects.create(**validated_data)
        return magasin

    def update(self, instance, validated_data):
        localisation_data = validated_data.pop('localisation', None)
        
        # Convertir les UUIDs en objets
        if 'responsable_magasin' in validated_data:
            if validated_data['responsable_magasin']:
                validated_data['responsable_magasin'] = Responsable_magasin.objects.get(id=validated_data['responsable_magasin'])
            else:
                validated_data['responsable_magasin'] = None
        if 'entreprise' in validated_data:
            validated_data['entreprise'] = Entreprise.objects.get(id=validated_data['entreprise'])
        if 'entrepot' in validated_data:
            if validated_data['entrepot']:
                validated_data['entrepot'] = Entrepot.objects.get(id=validated_data['entrepot'])
            else:
                validated_data['entrepot'] = None

        # Mettre à jour les autres champs du magasin
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        # Mettre à jour la localisation si fournie
        if localisation_data is not None:
            # Mettre à jour la localisation existante
            for attr, value in localisation_data.items():
                setattr(instance.localisation, attr, value)
            instance.localisation.save()

        instance.save()
        return instance
