from rest_framework import serializers
from .models import Entrepot, Location

class LocationSerializer(serializers.ModelSerializer):
    type_stock_display = serializers.CharField(source='get_type_stock_display', read_only=True)

    class Meta:
        model = Location
        fields = ['id', 'nom', 'description', 'type_stock', 'type_stock_display']

    def validate_type_stock(self, value):
        entrepot = self.context.get('entrepot')
        if entrepot and value not in entrepot.types_stock_acceptes:
            raise serializers.ValidationError(
                f"Cette location ne peut pas stocker des {dict(Entrepot.TYPES_STOCK)[value]}. "
                f"L'entrepôt n'accepte que : {', '.join([dict(Entrepot.TYPES_STOCK)[t] for t in entrepot.types_stock_acceptes])}"
            )
        return value

class EntrepotSerializer(serializers.ModelSerializer):
    locations = LocationSerializer(many=True, read_only=True)
    types_stock = serializers.SerializerMethodField()

    class Meta:
        model = Entrepot
        fields = ['id_entrepot', 'nom', 'adresse', 'capacite_stockage', 'statut', 
                 'types_stock', 'locations']

    def get_types_stock(self, obj):
        return [type_display for _, type_display in Entrepot.TYPES_STOCK]

    def validate_capacite_stockage(self, value):
        if value < 0:
            raise serializers.ValidationError("La capacité de stockage doit être positive.")
        return value